using System;
using System.Drawing;
using System.IO;

class Program
{
    static void Main()
    {
        string imagePath = @"OrbitPredictor\Resources\GDMap_05_A.jpg";
        
        try
        {
            Console.WriteLine($"测试图像文件: {imagePath}");
            
            // 检查文件是否存在
            if (!File.Exists(imagePath))
            {
                Console.WriteLine("错误: 文件不存在");
                return;
            }
            
            // 获取文件信息
            FileInfo fileInfo = new FileInfo(imagePath);
            Console.WriteLine($"文件大小: {fileInfo.Length} 字节");
            Console.WriteLine($"最后修改时间: {fileInfo.LastWriteTime}");
            
            // 尝试读取文件内容
            byte[] fileBytes = File.ReadAllBytes(imagePath);
            Console.WriteLine($"成功读取文件，字节数: {fileBytes.Length}");
            
            // 检查文件头是否为有效的JPEG格式
            if (fileBytes.Length >= 2)
            {
                if (fileBytes[0] == 0xFF && fileBytes[1] == 0xD8)
                {
                    Console.WriteLine("文件头检查: 有效的JPEG格式");
                }
                else
                {
                    Console.WriteLine($"文件头检查: 无效的JPEG格式 (头字节: {fileBytes[0]:X2} {fileBytes[1]:X2})");
                }
            }
            
            // 尝试使用System.Drawing.Bitmap加载图像
            using (var stream = new MemoryStream(fileBytes))
            {
                using (var bitmap = new Bitmap(stream))
                {
                    Console.WriteLine($"成功加载图像: {bitmap.Width}x{bitmap.Height} 像素");
                    Console.WriteLine($"像素格式: {bitmap.PixelFormat}");
                }
            }
            
            Console.WriteLine("图像文件验证成功！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.GetType().Name}: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"内部异常: {ex.InnerException.GetType().Name}: {ex.InnerException.Message}");
            }
        }
        
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
